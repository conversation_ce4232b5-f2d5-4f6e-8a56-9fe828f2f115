/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      update_service.dart
///
/// DESCRIPTION :    应用自动更新服务核心实现，负责版本检查、下载管理、安装管理等
///
/// AUTHOR :         wei
///
/// HISTORY :        04/08/2025 create

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/update_info.dart';
import '../models/update_config.dart';
import '../models/update_status.dart';
import '../utils/constants.dart';
import 'platform/platform_service_factory.dart';
import 'platform/platform_update_service.dart';
import 'log_service.dart';

/// UpdateService
///
/// PURPOSE:
///     应用自动更新服务的核心实现，提供完整的更新生命周期管理
///
/// FEATURES:
///     - 版本检查：与服务端API通信，获取最新版本信息
///     - 下载管理：支持断点续传、进度回调、文件校验
///     - 安装管理：平台特定的安装逻辑
///     - 定时检查：可配置的自动检查机制
///     - 状态管理：完整的状态流和事件通知
///     - 错误处理：重试机制和错误恢复
///     - 用户体验：跳过提醒、强制更新处理
///
/// USAGE:
///     UpdateService service = UpdateService(config);
///     await service.initialize();
///     service.updateStream.listen((info) => handleUpdate(info));
///     await service.checkForUpdate();
class UpdateService {
  final UpdateConfig config;
  final LogService _logService;
  final PlatformUpdateService _platformService;
  final Dio _dio;
  
  late final StreamController<UpdateInfo> _updateController;
  Timer? _periodicTimer;
  UpdateInfo _currentUpdateInfo = UpdateInfo.noUpdate();
  bool _isInitialized = false;
  bool _isCheckingUpdate = false;
  bool _isDownloading = false;
  CancelToken? _downloadCancelToken;

  /// UpdateService构造函数
  ///
  /// DESCRIPTION:
  ///     创建更新服务实例
  ///
  /// PARAMETERS:
  ///     config - 更新配置
  ///     logService - 日志服务（可选）
  UpdateService({
    required this.config,
    LogService? logService,
  }) : _logService = logService ?? LogService(),
       _platformService = PlatformServiceFactory.createUpdateService(),
       _dio = Dio() {
    _updateController = StreamController<UpdateInfo>.broadcast();
    _configureDio();
  }

  /// updateStream
  ///
  /// DESCRIPTION:
  ///     获取更新状态事件流
  ///
  /// RETURNS:
  ///     Stream<UpdateInfo> - 更新状态事件流
  Stream<UpdateInfo> get updateStream => _updateController.stream;

  /// currentUpdateInfo
  ///
  /// DESCRIPTION:
  ///     获取当前更新信息
  ///
  /// RETURNS:
  ///     UpdateInfo - 当前更新信息
  UpdateInfo get currentUpdateInfo => _currentUpdateInfo;

  /// isInitialized
  ///
  /// DESCRIPTION:
  ///     检查服务是否已初始化
  ///
  /// RETURNS:
  ///     bool - true表示已初始化，false表示未初始化
  bool get isInitialized => _isInitialized;

  /// platformService
  ///
  /// DESCRIPTION:
  ///     获取平台特定的更新服务实例
  ///
  /// RETURNS:
  ///     PlatformUpdateService - 平台更新服务
  PlatformUpdateService get platformService => _platformService;

  /// initialize
  ///
  /// DESCRIPTION:
  ///     初始化更新服务
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> initialize() async {
    if (_isInitialized) {
      return;
    }

    try {
      _logService.info('UpdateService', 'Initializing update service...');
      
      // 验证配置
      if (!config.isValid) {
        throw Exception('Invalid update configuration');
      }

      // 加载保存的更新信息
      await _loadSavedUpdateInfo();

      // 清理旧文件
      await _platformService.cleanupOldFiles();

      // 启动定时检查
      if (config.enableAutoCheck) {
        startPeriodicCheck();
      }

      // 启动时检查更新
      if (config.checkOnStartup) {
        Timer(Duration(seconds: config.startupCheckDelay), () {
          checkForUpdate();
        });
      }

      _isInitialized = true;
      _logService.info('UpdateService', 'Update service initialized successfully');
    } catch (e) {
      _logService.error('UpdateService', 'Failed to initialize update service', e);
      rethrow;
    }
  }

  /// checkForUpdate
  ///
  /// DESCRIPTION:
  ///     检查是否有可用更新
  ///     注意：iOS平台不查询下载服务器，直接返回无更新
  ///
  /// PARAMETERS:
  ///     domain - 用户域名（可选，如果不提供则尝试从AuthService获取）
  ///     forceCheck - 是否强制检查，忽略60分钟限制（默认false）
  ///
  /// RETURNS:
  ///     Future<UpdateInfo> - 更新信息
  Future<UpdateInfo> checkForUpdate({String? domain, bool forceCheck = false}) async {
    if (_isCheckingUpdate) {
      _logService.warning('UpdateService', 'Update check already in progress');
      return _currentUpdateInfo;
    }

    // 检查60分钟限制（除非强制检查）
    if (!forceCheck && !_shouldCheckForUpdate()) {
      _logService.info('UpdateService', 'Skipping update check due to 60-minute limit');
      return _currentUpdateInfo;
    }

    _isCheckingUpdate = true;

    // 记录检查开始时间戳
    await _recordCheckTimestamp();

    try {
      _logService.info('UpdateService', 'Checking for updates...');

      // 更新状态为检查中
      _updateStatus(UpdateStatus.checking);

      // 获取平台类型
      final platformType = _platformService.getPlatformType();

      // iOS平台不查询下载服务器，直接返回无更新
      if (platformType == 'ios') {
        _logService.info('UpdateService', 'iOS platform detected, skipping server check');

        _currentUpdateInfo = UpdateInfo.noUpdate().copyWith(
          lastCheckTime: DateTime.now(),
        );

        _updateController.add(_currentUpdateInfo);
        return _currentUpdateInfo;
      }

      // 获取当前版本（从关于界面的版本信息）
      final currentVersion = await _getCurrentVersionFromAbout();

      // 获取用户域名
      final userDomain = domain ?? await _getUserDomain();
      if (userDomain.isEmpty) {
        throw Exception('User domain is required for update check');
      }

      // 构建请求参数
      final requestData = {
        'type': platformType,
        'version': currentVersion,
        'domain': userDomain, // 使用用户界面的域名
      };

      _logService.debug('UpdateService', 'Sending update check request: $requestData');

      // 发送请求
      final response = await _dio.post(
        '${config.serverUrl}/update/check',
        data: requestData,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
          sendTimeout: config.timeout,
          receiveTimeout: config.timeout,
        ),
      );

      _logService.debug('UpdateService', 'Received update response: ${response.data}');

      // 解析响应
      final updateInfo = _parseUpdateResponse(response.data);

      // 保存更新信息
      await _saveUpdateInfo(updateInfo);

      // 更新当前状态
      _currentUpdateInfo = updateInfo.copyWith(
        lastCheckTime: DateTime.now(),
      );

      // 发送状态更新
      _updateController.add(_currentUpdateInfo);

      _logService.info('UpdateService',
        'Update check completed. Available: ${updateInfo.updateAvailable}');

      return _currentUpdateInfo;
    } catch (e) {
      _logService.error('UpdateService', 'Update check failed', e);

      // 更新为失败状态
      _currentUpdateInfo = _currentUpdateInfo.copyWith(
        status: UpdateStatus.failed,
        errorMessage: e.toString(),
        lastCheckTime: DateTime.now(),
      );

      _updateController.add(_currentUpdateInfo);
      rethrow;
    } finally {
      _isCheckingUpdate = false;
    }
  }

  /// downloadUpdate
  ///
  /// DESCRIPTION:
  ///     下载更新文件
  ///
  /// PARAMETERS:
  ///     updateInfo - 更新信息（可选，默认使用当前更新信息）
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> downloadUpdate([UpdateInfo? updateInfo]) async {
    updateInfo ??= _currentUpdateInfo;
    
    if (!updateInfo.canDownload) {
      throw Exception('Cannot download update: invalid state or missing information');
    }

    if (_isDownloading) {
      _logService.warning('UpdateService', 'Download already in progress');
      return;
    }

    _isDownloading = true;
    _downloadCancelToken = CancelToken();

    try {
      _logService.info('UpdateService', 'Starting update download...');
      
      // 检查网络连接
      if (!await _platformService.isNetworkAvailable()) {
        throw Exception('No network connection available');
      }

      // 检查WiFi限制
      if (config.wifiOnly && !await _platformService.isWifiConnected()) {
        throw Exception('WiFi connection required for download');
      }

      // 检查存储空间
      final availableSpace = await _platformService.getAvailableSpace();
      if (updateInfo.fileSize != null && availableSpace < updateInfo.fileSize! * 2) {
        throw Exception('Insufficient storage space');
      }

      // 准备下载路径
      final downloadPath = await _prepareDownloadPath(updateInfo);
      
      // 更新状态为下载中
      _updateStatus(UpdateStatus.downloading);

      // 开始下载
      await _downloadFile(updateInfo.downloadUrl!, downloadPath, updateInfo);

      // 验证文件
      if (updateInfo.hash != null && updateInfo.hashType != null) {
        final isValid = await _platformService.validateFile(
          downloadPath, 
          updateInfo.hash!, 
          updateInfo.hashType!
        );
        
        if (!isValid) {
          // 删除损坏的文件
          final file = File(downloadPath);
          if (await file.exists()) {
            await file.delete();
          }
          throw Exception('Downloaded file validation failed');
        }
      }

      // 更新状态为下载完成
      _currentUpdateInfo = _currentUpdateInfo.copyWith(
        status: UpdateStatus.downloaded,
        localFilePath: downloadPath,
        downloadProgress: 1.0,
      );

      await _saveUpdateInfo(_currentUpdateInfo);
      _updateController.add(_currentUpdateInfo);

      _logService.info('UpdateService', 'Update download completed successfully');
    } catch (e) {
      _logService.error('UpdateService', 'Update download failed', e);
      
      // 更新为失败状态
      _currentUpdateInfo = _currentUpdateInfo.copyWith(
        status: UpdateStatus.failed,
        errorMessage: e.toString(),
      );
      
      _updateController.add(_currentUpdateInfo);
      rethrow;
    } finally {
      _isDownloading = false;
      _downloadCancelToken = null;
    }
  }

  /// _configureDio
  ///
  /// DESCRIPTION:
  ///     配置Dio HTTP客户端
  void _configureDio() {
    _dio.options.connectTimeout = config.timeout;
    _dio.options.receiveTimeout = config.timeout;
    _dio.options.sendTimeout = config.timeout;
    
    // 添加重试拦截器
    _dio.interceptors.add(
      InterceptorsWrapper(
        onError: (error, handler) async {
          if (error.type == DioExceptionType.connectionTimeout ||
              error.type == DioExceptionType.receiveTimeout ||
              error.type == DioExceptionType.sendTimeout) {
            // 可以在这里实现重试逻辑
          }
          handler.next(error);
        },
      ),
    );
  }

  /// _updateStatus
  ///
  /// DESCRIPTION:
  ///     更新当前状态并发送事件
  ///
  /// PARAMETERS:
  ///     status - 新状态
  void _updateStatus(UpdateStatus status) {
    _currentUpdateInfo = _currentUpdateInfo.copyWith(status: status);
    _updateController.add(_currentUpdateInfo);
  }

  /// installUpdate
  ///
  /// DESCRIPTION:
  ///     安装更新
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> installUpdate() async {
    if (!_currentUpdateInfo.canInstall) {
      throw Exception('Cannot install update: invalid state or missing file');
    }

    try {
      _logService.info('UpdateService', 'Starting update installation...');

      // 更新状态为安装中
      _updateStatus(UpdateStatus.installing);

      // 检查权限
      if (!await _platformService.checkPermissions()) {
        final granted = await _platformService.requestPermissions();
        if (!granted) {
          throw Exception('Installation permissions not granted');
        }
      }

      // 执行平台特定的安装
      await _platformService.installUpdate(_currentUpdateInfo.localFilePath!);

      // 更新状态为安装完成
      _currentUpdateInfo = _currentUpdateInfo.copyWith(
        status: UpdateStatus.installed,
      );

      await _saveUpdateInfo(_currentUpdateInfo);
      _updateController.add(_currentUpdateInfo);

      _logService.info('UpdateService', 'Update installation completed');
    } catch (e) {
      _logService.error('UpdateService', 'Update installation failed', e);

      _currentUpdateInfo = _currentUpdateInfo.copyWith(
        status: UpdateStatus.failed,
        errorMessage: e.toString(),
      );

      _updateController.add(_currentUpdateInfo);
      rethrow;
    }
  }

  /// skipUpdate
  ///
  /// DESCRIPTION:
  ///     跳过当前更新
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> skipUpdate() async {
    _logService.info('UpdateService', 'Skipping current update');

    _currentUpdateInfo = _currentUpdateInfo.copyWith(
      status: UpdateStatus.skipped,
      skipCount: _currentUpdateInfo.skipCount + 1,
    );

    await _saveUpdateInfo(_currentUpdateInfo);
    _updateController.add(_currentUpdateInfo);
  }

  /// cancelUpdate
  ///
  /// DESCRIPTION:
  ///     取消当前更新操作
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> cancelUpdate() async {
    _logService.info('UpdateService', 'Cancelling current update');

    // 取消下载
    _downloadCancelToken?.cancel();

    _currentUpdateInfo = _currentUpdateInfo.copyWith(
      status: UpdateStatus.cancelled,
    );

    await _saveUpdateInfo(_currentUpdateInfo);
    _updateController.add(_currentUpdateInfo);
  }

  /// startPeriodicCheck
  ///
  /// DESCRIPTION:
  ///     启动定时检查更新
  void startPeriodicCheck() {
    if (_periodicTimer != null) {
      return;
    }

    _logService.info('UpdateService', 'Starting periodic update check');

    _periodicTimer = Timer.periodic(config.checkInterval, (timer) {
      // 添加随机延迟避免惊群效应
      final randomDelay = Random().nextInt(900); // 0-15分钟
      Timer(Duration(seconds: randomDelay), () {
        checkForUpdate().catchError((e) {
          _logService.warning('UpdateService', 'Periodic update check failed: $e');
          return _currentUpdateInfo; // 返回当前状态
        });
      });
    });
  }

  /// stopPeriodicCheck
  ///
  /// DESCRIPTION:
  ///     停止定时检查更新
  void stopPeriodicCheck() {
    if (_periodicTimer != null) {
      _logService.info('UpdateService', 'Stopping periodic update check');
      _periodicTimer!.cancel();
      _periodicTimer = null;
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /// _parseUpdateResponse
  ///
  /// DESCRIPTION:
  ///     解析服务端更新响应
  ///
  /// PARAMETERS:
  ///     responseData - 响应数据
  ///
  /// RETURNS:
  ///     UpdateInfo - 解析后的更新信息
  UpdateInfo _parseUpdateResponse(dynamic responseData) {
    try {
      final data = responseData as Map<String, dynamic>;
      return UpdateInfo.fromJson(data);
    } catch (e) {
      _logService.error('UpdateService', 'Failed to parse update response', e);
      return UpdateInfo.noUpdate();
    }
  }

  /// _loadSavedUpdateInfo
  ///
  /// DESCRIPTION:
  ///     加载保存的更新信息
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _loadSavedUpdateInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedData = prefs.getString('update_info');

      if (savedData != null) {
        final data = jsonDecode(savedData) as Map<String, dynamic>;
        _currentUpdateInfo = UpdateInfo.fromJson(data);
        _logService.debug('UpdateService', 'Loaded saved update info');
      }
    } catch (e) {
      _logService.warning('UpdateService', 'Failed to load saved update info: $e');
    }
  }

  /// _saveUpdateInfo
  ///
  /// DESCRIPTION:
  ///     保存更新信息到本地存储
  ///
  /// PARAMETERS:
  ///     updateInfo - 要保存的更新信息
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _saveUpdateInfo(UpdateInfo updateInfo) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = jsonEncode(updateInfo.toJson());
      await prefs.setString('update_info', data);
      _logService.debug('UpdateService', 'Saved update info');
    } catch (e) {
      _logService.warning('UpdateService', 'Failed to save update info: $e');
    }
  }

  /// _prepareDownloadPath
  ///
  /// DESCRIPTION:
  ///     准备下载文件路径
  ///
  /// PARAMETERS:
  ///     updateInfo - 更新信息
  ///
  /// RETURNS:
  ///     Future<String> - 下载文件路径
  Future<String> _prepareDownloadPath(UpdateInfo updateInfo) async {
    final downloadDir = _platformService.getDownloadDirectory();
    final directory = Directory(downloadDir);

    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }

    final extension = _platformService.getFileExtension();
    final fileName = 'update_${updateInfo.version}$extension';

    return '$downloadDir/$fileName';
  }

  /// _downloadFile
  ///
  /// DESCRIPTION:
  ///     下载文件并显示进度
  ///
  /// PARAMETERS:
  ///     url - 下载URL
  ///     savePath - 保存路径
  ///     updateInfo - 更新信息
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _downloadFile(String url, String savePath, UpdateInfo updateInfo) async {
    await _dio.download(
      url,
      savePath,
      cancelToken: _downloadCancelToken,
      onReceiveProgress: (received, total) {
        if (total > 0) {
          final progress = received / total;
          _currentUpdateInfo = _currentUpdateInfo.copyWith(
            downloadProgress: progress,
            fileSize: total,
          );
          _updateController.add(_currentUpdateInfo);
        }
      },
    );
  }

  /// _getCurrentVersionFromAbout
  ///
  /// DESCRIPTION:
  ///     获取关于界面显示的当前版本号
  ///
  /// RETURNS:
  ///     Future<String> - 当前版本号
  Future<String> _getCurrentVersionFromAbout() async {
    try {
      // 使用关于界面相同的版本获取逻辑
      return kAppVersion;
    } catch (e) {
      _logService.warning('UpdateService', 'Failed to get version from about screen: $e');
      return '1.0.0'; // 默认版本
    }
  }

  /// _getUserDomain
  ///
  /// DESCRIPTION:
  ///     获取用户界面的域名信息
  ///
  /// RETURNS:
  ///     Future<String> - 用户域名
  Future<String> _getUserDomain() async {
    try {
      // 从SharedPreferences获取保存的域名（与用户界面一致）
      final prefs = await SharedPreferences.getInstance();
      final domain = prefs.getString('domain') ?? '';
      return domain;
    } catch (e) {
      _logService.warning('UpdateService', 'Failed to get user domain: $e');
      return '';
    }
  }

  /// _shouldCheckForUpdate
  ///
  /// DESCRIPTION:
  ///     检查是否应该进行更新检查（60分钟限制）
  ///
  /// RETURNS:
  ///     bool - true表示可以检查，false表示需要等待
  bool _shouldCheckForUpdate() {
    final lastCheckTime = _currentUpdateInfo.lastCheckTime;
    if (lastCheckTime == null) {
      return true; // 从未检查过，可以检查
    }

    final now = DateTime.now();
    final timeDifference = now.difference(lastCheckTime);
    const minInterval = Duration(minutes: 60);

    return timeDifference >= minInterval;
  }

  /// _recordCheckTimestamp
  ///
  /// DESCRIPTION:
  ///     记录检查开始的时间戳
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _recordCheckTimestamp() async {
    try {
      final now = DateTime.now();
      _currentUpdateInfo = _currentUpdateInfo.copyWith(
        lastCheckTime: now,
      );
      await _saveUpdateInfo(_currentUpdateInfo);
      _logService.debug('UpdateService', 'Recorded update check timestamp: $now');
    } catch (e) {
      _logService.warning('UpdateService', 'Failed to record check timestamp: $e');
    }
  }

  /// dispose
  ///
  /// DESCRIPTION:
  ///     释放资源和清理
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> dispose() async {
    _logService.info('UpdateService', 'Disposing update service...');

    stopPeriodicCheck();

    // 取消正在进行的下载
    _downloadCancelToken?.cancel();

    await _updateController.close();
    _dio.close();

    _isInitialized = false;
    _logService.info('UpdateService', 'Update service disposed');
  }
}
