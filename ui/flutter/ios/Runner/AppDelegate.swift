/**
 * FILE: AppDelegate.swift
 *
 * DESCRIPTION:
 *     iOS AppDelegate with Platform Channel integration.
 *     Initializes universal Platform Channel handler from ItForceCore.
 *     Uses a single FlutterEngine instance to avoid lifecycle crashes.
 *
 * AUTHOR: wei
 * HISTORY:
 *     23/06/2025 - integrate universal Platform Channel handler
 *     01/07/2025 - fix engine binding crash and plugin registration issue
 *     02/07/2025 - unify FlutterEngine management to fix crash on app restart
 */

import Flutter
import UIKit
import ItForceCore
import os.log

@main
@objc public class AppDelegate: FlutterAppDelegate {

    /// 全局FlutterEngine，保证生命周期贯穿整个App
    private lazy var flutterEngine: FlutterEngine = {
        let engine = FlutterEngine(name: "itforce_engine")
        engine.run()
        GeneratedPluginRegistrant.register(with: engine)
        // NSLog("🔥 [AppDelegate] FlutterEngine initialized and plugins registered") // Debug NSLog commented for production
        return engine
    }()

    private var platformChannelHandler: PlatformChannelHandler?

    // MARK: - Screen Lock/Unlock Detection Properties

    /// 记录上次锁屏时间，用于计算锁屏时长
    private var lastLockTime: Date?

    /// 记录VPN在锁屏前的连接状态
    private var wasVPNConnectedBeforeLock: Bool = false

    /// 防止频繁重连的时间间隔（秒）
    private let minimumReconnectInterval: TimeInterval = 10.0

    /// 上次重连时间
    private var lastReconnectTime: Date?

    /// 防止重复处理解锁事件的标志
    private var isProcessingUnlock: Bool = false

    /// 防止重复重连检查的标志
    private var isReconnectionCheckInProgress: Bool = false



    public override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        // NSLog("🔥 [AppDelegate] didFinishLaunchingWithOptions called") // Debug NSLog commented for production

        // 使用 FlutterEngine 创建 FlutterViewController 并作为 root
        if window == nil {
            window = UIWindow(frame: UIScreen.main.bounds)
        }

        let flutterViewController = FlutterViewController(engine: flutterEngine, nibName: nil, bundle: nil)
        window?.rootViewController = flutterViewController
        window?.makeKeyAndVisible()

        // Call super after setting up FlutterViewController
        let result = super.application(application, didFinishLaunchingWithOptions: launchOptions)

        // 异步设置初始化通道，等待 UI 完成加载
        DispatchQueue.main.async {
            self.setupInitializationChannel()
        }

        // 设置锁屏/唤醒事件监听
        setupScreenLockUnlockNotifications()

        return result
    }

    /// 初始化时只注册一个简单的 init channel，等待用户登录时再创建详细 handler
    private func setupInitializationChannel() {
        guard let controller = findFlutterViewController() else {
            // NSLog("❌ [AppDelegate] FlutterViewController not found") // Debug NSLog commented for production
            return
        }

        let messenger = controller.engine.binaryMessenger

        let initChannel = FlutterMethodChannel(
            name: "panabit_client/init",
            binaryMessenger: messenger
        )

        initChannel.setMethodCallHandler { [weak self] call, result in
            if call.method == "initializeBackend" {
                // NSLog("🔥 Received initializeBackend") // Debug NSLog commented for production

                if let handler = self?.platformChannelHandler {
                    // NSLog("✅ Reusing existing PlatformChannelHandler") // Debug NSLog commented for production
                    Task {
                        await handler.handleInitializeBackendFromAppDelegate(result: result as Any)
                    }
                } else {
                    // NSLog("🔧 Creating new PlatformChannelHandler") // Debug NSLog commented for production
                    if let handler = self?.setupPlatformChannelHandler() {
                        Task {
                            await handler.handleInitializeBackendFromAppDelegate(result: result as Any)
                        }
                    } else {
                        result(FlutterError(code: "INIT_FAILED", message: "Handler creation failed", details: nil))
                    }
                }
            } else {
                result(FlutterMethodNotImplemented)
            }
        }

        // NSLog("✅ Initialization channel configured: panabit_client/init") // Debug NSLog commented for production
    }

    /// 登录时调用，创建并配置 PlatformChannelHandler
    public func setupPlatformChannelHandler() -> PlatformChannelHandler? {
        // NSLog("🛠️ Setting up PlatformChannelHandler...") // Debug NSLog commented for production

        guard Thread.isMainThread else {
            var result: PlatformChannelHandler?
            DispatchQueue.main.sync {
                result = self.setupPlatformChannelHandler()
            }
            return result
        }

        guard let controller = findFlutterViewController() else {
            // NSLog("❌ FlutterViewController not found during login setup") // Debug NSLog commented for production
            return nil
        }

        let messenger = controller.engine.binaryMessenger

        let handler = PlatformChannelHandler()
        handler.configureFlutter(binaryMessenger: messenger)
        platformChannelHandler = handler

        // NSLog("✅ PlatformChannelHandler configured") // Debug NSLog commented for production
        return handler
    }

    /// 查找 FlutterViewController，支持多Scene环境
    private func findFlutterViewController() -> FlutterViewController? {
        if let vc = window?.rootViewController as? FlutterViewController {
            return vc
        }

        for scene in UIApplication.shared.connectedScenes {
            if let windowScene = scene as? UIWindowScene {
                for win in windowScene.windows {
                    if let vc = win.rootViewController as? FlutterViewController {
                        return vc
                    }
                }
            }
        }
        return nil
    }

    /// 强制锁定竖屏方向
    public override func application(
        _ application: UIApplication,
        supportedInterfaceOrientationsFor window: UIWindow?
    ) -> UIInterfaceOrientationMask {
        return .portrait
    }

    // MARK: - Screen Lock/Unlock Detection

    /**
     * NAME: setupScreenLockUnlockNotifications
     *
     * DESCRIPTION:
     *     设置锁屏/唤醒事件监听，使用Apple官方推荐的API
     *     遵循Apple官方指导原则，不使用私有API
     */
    private func setupScreenLockUnlockNotifications() {
        let notificationCenter = NotificationCenter.default

        // 监听设备解锁事件（仅在设备有密码保护时有效）
        notificationCenter.addObserver(
            self,
            selector: #selector(deviceDidUnlock),
            name: UIApplication.protectedDataDidBecomeAvailableNotification,
            object: nil
        )

        // 监听设备即将锁定事件
        notificationCenter.addObserver(
            self,
            selector: #selector(deviceWillLock),
            name: UIApplication.protectedDataWillBecomeUnavailableNotification,
            object: nil
        )

        // 监听应用回到前台事件（通过health检查VPN extension健康状况）
        notificationCenter.addObserver(
            self,
            selector: #selector(handleApplicationWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )

        // 监听应用进入后台事件
        notificationCenter.addObserver(
            self,
            selector: #selector(handleApplicationDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )

        NSLog("🔒 [AppDelegate] Screen lock/unlock notifications setup completed")

        // 统一增强：确保通知监听器不被编译器优化掉
        // 强制保持对通知中心的引用，防止被编译器优化
        objc_setAssociatedObject(self, "notificationCenter", notificationCenter, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)

        // 验证通知监听器是否正确设置
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let observers = notificationCenter.description
            os_log("🔒 [UNIFIED] Notification observers verified: %{public}@", log: OSLog.default, type: .info, observers)
        }
    }

    /**
     * NAME: deviceWillLock
     *
     * DESCRIPTION:
     *     设备即将锁定时的处理逻辑
     *     记录锁屏时间和VPN连接状态，不主动断开VPN
     *     VPN Extension会通过disconnectOnSleep自动处理断开
     */
    @objc private func deviceWillLock() {
        lastLockTime = Date()
        print("🔍 [SleepMonitor_DEBUG] Device will lock notification received at: \(lastLockTime!)")

        // 统一增强：确保通知方法被正确调用
        os_log("🔒 [UNIFIED] Device will lock notification triggered at: %{public}@", log: OSLog.default, type: .info, "\(lastLockTime!)")

        // 只记录当前VPN连接状态，不主动断开
        // VPN Extension已设置disconnectOnSleep，会自动处理sleep时的断开
        if let handler = platformChannelHandler {
            print("🔍 [SleepMonitor_DEBUG] PlatformChannelHandler available, recording VPN status")
            Task {
                let isConnected = await handler.isVPNConnected()
                let vpnState = await handler.getVPNConnectionState()
                wasVPNConnectedBeforeLock = isConnected

                print("🔍 [SleepMonitor_DEBUG] VPN status before lock - connected: \(isConnected), state: \(vpnState)")
                NSLog("🔒 [AppDelegate] Device will lock - VPN was connected: \(wasVPNConnectedBeforeLock)")

                os_log("🔒 [UNIFIED] VPN state before lock: connected=%{public}@", log: OSLog.default, type: .info, "\(wasVPNConnectedBeforeLock)")

                // 不主动断开VPN - 由VPN Extension的disconnectOnSleep和sleep()方法处理
                print("🔍 [SleepMonitor_DEBUG] VPN Extension will handle disconnect on sleep automatically")
            }
        } else {
            print("🔍 [SleepMonitor_DEBUG] PlatformChannelHandler not available")
        }
    }

    /**
     * NAME: deviceDidUnlock
     *
     * DESCRIPTION:
     *     设备解锁时的处理逻辑
     *     检查VPN连接状态并触发重连（如果VPN Extension因sleep断开）
     *     添加防重复处理机制
     */
    @objc private func deviceDidUnlock() {
        // 防止重复处理解锁事件
        guard !isProcessingUnlock else {
            print("🔍 [SleepReconnect_DEBUG] Device unlock already being processed, skipping duplicate")
            NSLog("⚠️ [AppDelegate] Device unlock already being processed, skipping duplicate")
            return
        }

        isProcessingUnlock = true

        let unlockTime = Date()
        let lockDuration = lastLockTime?.timeIntervalSince(unlockTime) ?? 0

        print("🔍 [SleepReconnect_DEBUG] Device unlock notification received at: \(unlockTime)")
        print("🔍 [SleepReconnect_DEBUG] Lock time was: \(lastLockTime?.description ?? "unknown")")
        print("🔍 [SleepReconnect_DEBUG] Calculated lock duration: \(abs(lockDuration))s")

        NSLog("🔓 [AppDelegate] Device unlocked - lock duration: \(abs(lockDuration))s")

        // 统一增强日志确保可见性
        os_log("🔓 [UNIFIED] Device unlocked - lock duration: %{public}@s", log: OSLog.default, type: .info, "\(abs(lockDuration))")

        // 触发重连以恢复VPN Extension因sleep断开的连接
        if let handler = platformChannelHandler {
            Task {
                let success = await handler.triggerScreenLockRecoveryReconnection(
                    reason: "screen_unlock_after_sleep",
                    lockDuration: abs(lockDuration)
                )
                print("🔍 [SleepReconnect_DEBUG] Screen unlock reconnection result: \(success)")
            }
        }

        // 统一增强：确保重连逻辑在后台也能执行
        // 使用后台任务保证重连逻辑完成
        var backgroundTaskID: UIBackgroundTaskIdentifier = .invalid
        backgroundTaskID = UIApplication.shared.beginBackgroundTask(withName: "VPN-Reconnect-Cleanup") {
            // 后台任务即将过期时的清理
            if backgroundTaskID != .invalid {
                UIApplication.shared.endBackgroundTask(backgroundTaskID)
                backgroundTaskID = .invalid
            }
        }

        // 给VPN重连更充足的时间 - 延长到15秒
        // 考虑到重连可能需要：断开(2s) + 重试连接(10s*2次) = 最多22秒
        DispatchQueue.main.asyncAfter(deadline: .now() + 15.0) {
            self.isProcessingUnlock = false
            print("🔍 [Reconnect_DEBUG] Unlock processing flag reset after 15s delay (allowing sufficient reconnection time)")

            // 结束后台任务
            if backgroundTaskID != .invalid {
                UIApplication.shared.endBackgroundTask(backgroundTaskID)
                backgroundTaskID = .invalid
            }
        }
    }





    /**
     * NAME: handleApplicationWillEnterForeground
     *
     * DESCRIPTION:
     *     应用回到前台时的处理逻辑
     *     检查VPN Extension是否因sleep断开，如果需要则触发重连
     *     通过health检查确保VPN Extension健康状况
     */
    @objc private func handleApplicationWillEnterForeground() {
        print("🔍 [ForegroundReconnect_DEBUG] Application will enter foreground notification received")
        NSLog("📱 [AppDelegate] Application will enter foreground - checking VPN health")

        guard let handler = platformChannelHandler else {
            print("🔍 [ForegroundReconnect_DEBUG] PlatformChannelHandler not available")
            NSLog("⚠️ [AppDelegate] PlatformChannelHandler not available for health check")
            return
        }

        // 延迟检查，确保应用完全激活和VPN Extension稳定
        print("🔍 [ForegroundReconnect_DEBUG] Scheduling delayed VPN health check (2.0s delay for VPN stability)")
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            print("🔍 [ForegroundReconnect_DEBUG] Executing VPN health check for app_foreground")

            Task {
                await self.checkVPNHealthAndReconnectIfNeeded(handler: handler)
            }
        }
    }

    /**
     * NAME: checkVPNHealthAndReconnectIfNeeded
     *
     * DESCRIPTION:
     *     检查VPN Extension健康状况，处理因sleep断开的情况
     *     如果VPN Extension无响应则自动重连
     *
     * PARAMETERS:
     *     handler - PlatformChannelHandler实例
     */
    private func checkVPNHealthAndReconnectIfNeeded(handler: PlatformChannelHandler) async {
        print("🔍 [ForegroundReconnect_DEBUG] Starting VPN health check")

        // 首先检查VPN是否应该连接（锁屏前是否连接）
        guard wasVPNConnectedBeforeLock else {
            print("🔍 [ForegroundReconnect_DEBUG] VPN was not connected before background, skipping health check")
            NSLog("📱 [AppDelegate] VPN was not connected before background, no health check needed")
            return
        }

        // 检查当前VPN状态
        let currentState = await handler.getVPNConnectionState()
        print("🔍 [ForegroundReconnect_DEBUG] Current VPN state: \(currentState)")

        // 如果VPN不是连接状态，可能是VPN Extension因sleep断开，触发重连
        if !currentState.contains("connected") {
            print("🔍 [ForegroundReconnect_DEBUG] VPN not connected (possibly disconnected by sleep), triggering reconnection")
            NSLog("📱 [AppDelegate] VPN not connected on foreground, triggering reconnection")

            let success = await handler.triggerScreenLockRecoveryReconnection(
                reason: "app_foreground_sleep_recovery",
                lockDuration: 0
            )

            print("🔍 [ForegroundReconnect_DEBUG] Foreground reconnection result: \(success)")
            return
        }

        // VPN已连接，进行health检查（3秒超时，给VPN Extension更多响应时间）
        print("🔍 [Health_DEBUG] VPN connected, performing health check with 3s timeout")

        do {
            let healthResult = try await withThrowingTaskGroup(of: [String: Any].self) { group in
                // 添加health检查任务
                group.addTask {
                    return await handler.performHealthCheck()
                }

                // 添加超时任务
                group.addTask {
                    try await Task.sleep(nanoseconds: 3_000_000_000) // 3 seconds
                    throw NSError(domain: "HealthCheck", code: -1001, userInfo: [
                        NSLocalizedDescriptionKey: "Health check timeout after 3 seconds"
                    ])
                }

                // 等待第一个完成的任务
                let result = try await group.next()!
                group.cancelAll()
                return result
            }

            // Health检查成功
            print("🔍 [Health_DEBUG] ✅ Health check successful: \(healthResult)")
            NSLog("📱 [AppDelegate] VPN health check passed")

        } catch {
            // Health检查超时或失败，触发重连
            print("🔍 [Health_DEBUG] ❌ Health check failed: \(error.localizedDescription)")
            NSLog("📱 [AppDelegate] VPN health check failed, triggering reconnection: \(error.localizedDescription)")

            let success = await handler.triggerScreenLockRecoveryReconnection(
                reason: "app_foreground_health_timeout",
                lockDuration: 0
            )

            print("🔍 [Health_DEBUG] Health timeout reconnection result: \(success)")
        }
    }

    /**
     * NAME: handleApplicationDidEnterBackground
     *
     * DESCRIPTION:
     *     应用进入后台时的处理逻辑
     *     记录VPN状态以备后续恢复使用
     */
    @objc private func handleApplicationDidEnterBackground() {
        print("🔍 [Reconnect_DEBUG] Application did enter background notification received")
        NSLog("📱 [AppDelegate] Application did enter background")

        // 记录当前VPN连接状态
        if let handler = platformChannelHandler {
            print("🔍 [Reconnect_DEBUG] PlatformChannelHandler available, checking VPN status for background")
            Task {
                let isConnected = await handler.isVPNConnected()
                let vpnState = await handler.getVPNConnectionState()
                wasVPNConnectedBeforeLock = isConnected

                print("🔍 [Reconnect_DEBUG] VPN status when backgrounded - connected: \(isConnected), state: \(vpnState)")
                NSLog("📱 [AppDelegate] App backgrounded - VPN connected: \(isConnected)")
            }
        } else {
            print("🔍 [Reconnect_DEBUG] PlatformChannelHandler not available for background check")
        }
    }

    /**
     * NAME: checkAndTriggerVPNReconnectionIfNeeded
     *
     * DESCRIPTION:
     *     检查是否需要触发VPN重连，并执行重连逻辑
     *     实现智能重连判断，避免不必要的重连操作
     *
     * PARAMETERS:
     *     reason - 触发重连检查的原因
     *     lockDuration - 锁屏时长（秒）
     */
    private func checkAndTriggerVPNReconnectionIfNeeded(reason: String, lockDuration: TimeInterval) {
        print("🔍 [Reconnect_DEBUG] === VPN Reconnection Check Started ===")
        print("🔍 [Reconnect_DEBUG] Reason: \(reason), Lock Duration: \(lockDuration)s")

        // 防止重复重连检查
        guard !isReconnectionCheckInProgress else {
            print("🔍 [Reconnect_DEBUG] ⚠️ Reconnection check already in progress, skipping duplicate")
            NSLog("⚠️ [AppDelegate] Reconnection check already in progress, skipping duplicate call")
            return
        }

        isReconnectionCheckInProgress = true
        defer {
            isReconnectionCheckInProgress = false
            print("🔍 [Reconnect_DEBUG] Reconnection check flag reset")
        }

        guard let handler = platformChannelHandler else {
            print("🔍 [Reconnect_DEBUG] PlatformChannelHandler is nil - cannot proceed")
            NSLog("⚠️ [AppDelegate] PlatformChannelHandler not available for VPN reconnection check")
            return
        }

        print("🔍 [Reconnect_DEBUG] PlatformChannelHandler available")

        // 检查是否在最小重连间隔内
        if let lastReconnect = lastReconnectTime {
            let timeSinceLastReconnect = Date().timeIntervalSince(lastReconnect)
            print("🔍 [Reconnect_DEBUG] Last reconnect time: \(lastReconnect)")
            print("🔍 [Reconnect_DEBUG] Time since last reconnect: \(timeSinceLastReconnect)s")
            print("🔍 [Reconnect_DEBUG] Minimum interval: \(minimumReconnectInterval)s")

            if timeSinceLastReconnect < minimumReconnectInterval {
                print("🔍 [Reconnect_DEBUG] Skipping - within minimum interval")
                NSLog("⏰ [AppDelegate] Skipping reconnection - too soon since last attempt (\(timeSinceLastReconnect)s < \(minimumReconnectInterval)s)")
                return
            }
        } else {
            print("🔍 [Reconnect_DEBUG] No previous reconnect time recorded")
        }

        print("🔍 [Reconnect_DEBUG] Starting async VPN status check")

        Task {
            do {
                print("🔍 [Reconnect_DEBUG] Fetching current VPN connection status...")

                // 获取当前VPN连接状态
                let isCurrentlyConnected = await handler.isVPNConnected()
                let vpnState = await handler.getVPNConnectionState()

                print("🔍 [Reconnect_DEBUG] VPN status fetched:")
                print("🔍 [Reconnect_DEBUG] - Was connected before lock: \(wasVPNConnectedBeforeLock)")
                print("🔍 [Reconnect_DEBUG] - Currently connected: \(isCurrentlyConnected)")
                print("🔍 [Reconnect_DEBUG] - Current state: \(vpnState)")

                NSLog("🔍 [AppDelegate] VPN reconnection check - reason: \(reason), lockDuration: \(lockDuration)s")
                NSLog("🔍 [AppDelegate] VPN state - wasConnectedBeforeLock: \(wasVPNConnectedBeforeLock), currentlyConnected: \(isCurrentlyConnected), state: \(vpnState)")

                print("🔍 [Reconnect_DEBUG] Evaluating reconnection conditions...")

                // 判断是否需要重连
                let shouldReconnect = shouldTriggerVPNReconnection(
                    reason: reason,
                    lockDuration: lockDuration,
                    wasConnectedBeforeLock: wasVPNConnectedBeforeLock,
                    isCurrentlyConnected: isCurrentlyConnected,
                    currentState: vpnState
                )

                print("🔍 [Reconnect_DEBUG] Reconnection decision: \(shouldReconnect)")

                if shouldReconnect {
                    print("🔍 [Reconnect_DEBUG] Conditions met - proceeding with reconnection")
                    NSLog("🔄 [AppDelegate] Triggering VPN reconnection due to \(reason)")
                    lastReconnectTime = Date()

                    print("🔍 [Reconnect_DEBUG] Updated last reconnect time to: \(lastReconnectTime!)")
                    print("🔍 [Reconnect_DEBUG] Calling triggerScreenLockRecoveryReconnection...")

                    // 统一增强：使用后台任务保护重连过程
                    var backgroundTaskID: UIBackgroundTaskIdentifier = .invalid
                    backgroundTaskID = UIApplication.shared.beginBackgroundTask(withName: "VPN-Reconnect-Process") {
                        if backgroundTaskID != .invalid {
                            UIApplication.shared.endBackgroundTask(backgroundTaskID)
                            backgroundTaskID = .invalid
                        }
                    }

                    // 触发VPN重连
                    let success = await handler.triggerScreenLockRecoveryReconnection(
                        reason: reason,
                        lockDuration: lockDuration
                    )

                    print("🔍 [Reconnect_DEBUG] Reconnection result: \(success)")

                    if success {
                        NSLog("✅ [AppDelegate] VPN reconnection triggered successfully")
                        os_log("✅ [UNIFIED] VPN reconnection triggered successfully for reason: %{public}@", log: OSLog.default, type: .info, reason)
                    } else {
                        NSLog("❌ [AppDelegate] VPN reconnection failed to trigger")
                        os_log("❌ [UNIFIED] VPN reconnection failed to trigger for reason: %{public}@", log: OSLog.default, type: .error, reason)
                    }

                    // 结束后台任务
                    if backgroundTaskID != .invalid {
                        UIApplication.shared.endBackgroundTask(backgroundTaskID)
                        backgroundTaskID = .invalid
                    }
                } else {
                    print("🔍 [Reconnect_DEBUG] Conditions not met - skipping reconnection")
                    NSLog("ℹ️ [AppDelegate] VPN reconnection not needed - conditions not met")
                }

            } catch {
                print("🔍 [Reconnect_DEBUG] Error during VPN reconnection check: \(error)")
                NSLog("❌ [AppDelegate] Error during VPN reconnection check: \(error.localizedDescription)")
            }

            print("🔍 [Reconnect_DEBUG] === VPN Reconnection Check Completed ===")
        }
    }

    /**
     * NAME: shouldTriggerVPNReconnection
     *
     * DESCRIPTION:
     *     判断是否应该触发VPN重连的智能逻辑
     *
     * PARAMETERS:
     *     reason - 触发原因
     *     lockDuration - 锁屏时长
     *     wasConnectedBeforeLock - 锁屏前是否连接
     *     isCurrentlyConnected - 当前是否连接
     *     currentState - 当前VPN状态
     *
     * RETURNS:
     *     Bool - 是否应该触发重连
     */
    private func shouldTriggerVPNReconnection(
        reason: String,
        lockDuration: TimeInterval,
        wasConnectedBeforeLock: Bool,
        isCurrentlyConnected: Bool,
        currentState: String
    ) -> Bool {

        print("🔍 [Reconnect_DEBUG] === Evaluating Reconnection Conditions ===")
        print("🔍 [Reconnect_DEBUG] Input parameters:")
        print("🔍 [Reconnect_DEBUG] - reason: \(reason)")
        print("🔍 [Reconnect_DEBUG] - lockDuration: \(lockDuration)s")
        print("🔍 [Reconnect_DEBUG] - wasConnectedBeforeLock: \(wasConnectedBeforeLock)")
        print("🔍 [Reconnect_DEBUG] - isCurrentlyConnected: \(isCurrentlyConnected)")
        print("🔍 [Reconnect_DEBUG] - currentState: \(currentState)")

        // 条件1：VPN之前必须是连接状态
        print("🔍 [Reconnect_DEBUG] Checking condition 1: Was VPN connected before lock?")
        guard wasConnectedBeforeLock else {
            print("🔍 [Reconnect_DEBUG] ❌ Condition 1 failed: VPN was not connected before lock")
            NSLog("🚫 [AppDelegate] No reconnection needed - VPN was not connected before lock")
            return false
        }
        print("🔍 [Reconnect_DEBUG] ✅ Condition 1 passed: VPN was connected before lock")

        // 条件2：对于屏幕解锁，锁屏时间应该足够长（避免短暂锁屏触发重连）
        print("🔍 [Reconnect_DEBUG] Checking condition 2: Lock duration threshold")
        if reason == "screen_unlock" && lockDuration < 30.0 {
            print("🔍 [Reconnect_DEBUG] ❌ Condition 2 failed: Lock duration too short (\(lockDuration)s < 30.0s)")
            NSLog("🚫 [AppDelegate] No reconnection needed - lock duration too short (\(lockDuration)s)")
            return false
        }
        print("🔍 [Reconnect_DEBUG] ✅ Condition 2 passed: Lock duration acceptable or not screen_unlock")

        // 条件3：避免重复重连已连接的VPN（优化后的检查）
        print("🔍 [Reconnect_DEBUG] Checking condition 3: Current VPN state analysis")
        if isCurrentlyConnected && currentState == "connected" {
            print("🔍 [Reconnect_DEBUG] ⚠️ VPN is currently connected and healthy")
            // 对于已连接且健康的VPN，只在锁屏时间很长时才重连（可能网络已切换）
            if reason == "screen_unlock" && lockDuration < 30.0 {
                print("🔍 [Reconnect_DEBUG] ❌ Condition 3 failed: VPN healthy and lock duration not long enough (\(lockDuration)s < 60.0s)")
                NSLog("🚫 [AppDelegate] No reconnection needed - VPN is healthy and lock duration not very long")
                return false
            }
            print("🔍 [Reconnect_DEBUG] ✅ Condition 3 passed: Very long lock duration, may need reconnection for network changes")
        } else {
            print("🔍 [Reconnect_DEBUG] ✅ Condition 3 passed: VPN not in healthy connected state (\(currentState))")
        }

        // 条件4：检查连接状态并处理卡住的情况
        print("🔍 [Reconnect_DEBUG] Checking condition 4: Connection state analysis")
        if currentState.contains("connecting") || currentState.contains("reconnecting") {
            print("🔍 [Reconnect_DEBUG] ⚠️ VPN is in connecting state, checking if stuck...")

            // 检查是否是长时间卡住的连接状态（参考Android的超时机制）
            if let lastReconnect = lastReconnectTime {
                let timeSinceLastReconnect = Date().timeIntervalSince(lastReconnect)
                print("🔍 [Reconnect_DEBUG] Time since last reconnect attempt: \(timeSinceLastReconnect)s")

                // 如果连接状态超过30秒，认为是卡住了，需要强制重置
                if timeSinceLastReconnect > 30.0 {
                    print("🔍 [Reconnect_DEBUG] ✅ Condition 4 passed: Connecting state stuck for \(timeSinceLastReconnect)s, will force reset")
                    NSLog("⚠️ [AppDelegate] VPN stuck in connecting state for \(timeSinceLastReconnect)s, will force reset and reconnect")
                } else {
                    print("🔍 [Reconnect_DEBUG] ❌ Condition 4 failed: VPN connecting but not stuck yet (\(timeSinceLastReconnect)s < 30.0s)")
                    NSLog("🚫 [AppDelegate] No reconnection needed - VPN is connecting but not stuck yet")
                    return false
                }
            } else {
                // 没有上次重连时间记录，可能是应用重启后的状态，允许重连
                print("🔍 [Reconnect_DEBUG] ✅ Condition 4 passed: No previous reconnect time, allowing reset of stuck connecting state")
                NSLog("⚠️ [AppDelegate] VPN in connecting state with no previous reconnect time, will reset")
            }
        } else {
            print("🔍 [Reconnect_DEBUG] ✅ Condition 4 passed: VPN not in connecting state")
        }

        // 满足重连条件
        print("🔍 [Reconnect_DEBUG] ✅ All conditions met - reconnection should proceed")
        NSLog("✅ [AppDelegate] VPN reconnection conditions met")
        return true
    }

    // MARK: - Cleanup

    deinit {
        // 清理通知监听
        NotificationCenter.default.removeObserver(self)
    }
}