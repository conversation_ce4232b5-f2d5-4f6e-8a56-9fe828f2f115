/**
 * FILE: AppDelegate.swift
 *
 * DESCRIPTION:
 *     iOS AppDelegate with Platform Channel integration.
 *     Initializes universal Platform Channel handler from ItForceCore.
 *     Uses a single FlutterEngine instance to avoid lifecycle crashes.
 *
 * AUTHOR: wei
 * HISTORY:
 *     23/06/2025 - integrate universal Platform Channel handler
 *     01/07/2025 - fix engine binding crash and plugin registration issue
 *     02/07/2025 - unify FlutterEngine management to fix crash on app restart
 */

import Flutter
import UIKit
import ItForceCore
import os.log

@main
@objc public class AppDelegate: FlutterAppDelegate {

    /// 全局FlutterEngine，保证生命周期贯穿整个App
    private lazy var flutterEngine: FlutterEngine = {
        let engine = FlutterEngine(name: "itforce_engine")
        engine.run()
        GeneratedPluginRegistrant.register(with: engine)
        // NSLog("🔥 [AppDelegate] FlutterEngine initialized and plugins registered") // Debug NSLog commented for production
        return engine
    }()

    private var platformChannelHandler: PlatformChannelHandler?

    // MARK: - Simple Reconnection Properties

    /// 记录VPN在后台前的连接状态
    private var wasVPNConnectedBeforeLock: Bool = false

    /// 防止重复处理解锁事件的标志
    private var isProcessingUnlock: Bool = false

    /// 防止频繁重连的时间间隔（秒）
    private let minimumReconnectInterval: TimeInterval = 10.0

    /// 上次重连时间
    private var lastReconnectTime: Date?

    /// 防止重复重连检查的标志
    private var isReconnectionCheckInProgress: Bool = false



    public override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        // NSLog("🔥 [AppDelegate] didFinishLaunchingWithOptions called") // Debug NSLog commented for production

        // 使用 FlutterEngine 创建 FlutterViewController 并作为 root
        if window == nil {
            window = UIWindow(frame: UIScreen.main.bounds)
        }

        let flutterViewController = FlutterViewController(engine: flutterEngine, nibName: nil, bundle: nil)
        window?.rootViewController = flutterViewController
        window?.makeKeyAndVisible()

        // Call super after setting up FlutterViewController
        let result = super.application(application, didFinishLaunchingWithOptions: launchOptions)

        // 异步设置初始化通道，等待 UI 完成加载
        DispatchQueue.main.async {
            self.setupInitializationChannel()
        }

        // 设置锁屏/唤醒事件监听
        setupScreenLockUnlockNotifications()

        return result
    }

    /// 初始化时只注册一个简单的 init channel，等待用户登录时再创建详细 handler
    private func setupInitializationChannel() {
        guard let controller = findFlutterViewController() else {
            // NSLog("❌ [AppDelegate] FlutterViewController not found") // Debug NSLog commented for production
            return
        }

        let messenger = controller.engine.binaryMessenger

        let initChannel = FlutterMethodChannel(
            name: "panabit_client/init",
            binaryMessenger: messenger
        )

        initChannel.setMethodCallHandler { [weak self] call, result in
            if call.method == "initializeBackend" {
                // NSLog("🔥 Received initializeBackend") // Debug NSLog commented for production

                if let handler = self?.platformChannelHandler {
                    // NSLog("✅ Reusing existing PlatformChannelHandler") // Debug NSLog commented for production
                    Task {
                        await handler.handleInitializeBackendFromAppDelegate(result: result as Any)
                    }
                } else {
                    // NSLog("🔧 Creating new PlatformChannelHandler") // Debug NSLog commented for production
                    if let handler = self?.setupPlatformChannelHandler() {
                        Task {
                            await handler.handleInitializeBackendFromAppDelegate(result: result as Any)
                        }
                    } else {
                        result(FlutterError(code: "INIT_FAILED", message: "Handler creation failed", details: nil))
                    }
                }
            } else {
                result(FlutterMethodNotImplemented)
            }
        }

        // NSLog("✅ Initialization channel configured: panabit_client/init") // Debug NSLog commented for production
    }

    /// 登录时调用，创建并配置 PlatformChannelHandler
    public func setupPlatformChannelHandler() -> PlatformChannelHandler? {
        // NSLog("🛠️ Setting up PlatformChannelHandler...") // Debug NSLog commented for production

        guard Thread.isMainThread else {
            var result: PlatformChannelHandler?
            DispatchQueue.main.sync {
                result = self.setupPlatformChannelHandler()
            }
            return result
        }

        guard let controller = findFlutterViewController() else {
            // NSLog("❌ FlutterViewController not found during login setup") // Debug NSLog commented for production
            return nil
        }

        let messenger = controller.engine.binaryMessenger

        let handler = PlatformChannelHandler()
        handler.configureFlutter(binaryMessenger: messenger)
        platformChannelHandler = handler

        // NSLog("✅ PlatformChannelHandler configured") // Debug NSLog commented for production
        return handler
    }

    /// 查找 FlutterViewController，支持多Scene环境
    private func findFlutterViewController() -> FlutterViewController? {
        if let vc = window?.rootViewController as? FlutterViewController {
            return vc
        }

        for scene in UIApplication.shared.connectedScenes {
            if let windowScene = scene as? UIWindowScene {
                for win in windowScene.windows {
                    if let vc = win.rootViewController as? FlutterViewController {
                        return vc
                    }
                }
            }
        }
        return nil
    }

    /// 强制锁定竖屏方向
    public override func application(
        _ application: UIApplication,
        supportedInterfaceOrientationsFor window: UIWindow?
    ) -> UIInterfaceOrientationMask {
        return .portrait
    }

    // MARK: - Screen Lock/Unlock Detection

    /**
     * NAME: setupScreenLockUnlockNotifications
     *
     * DESCRIPTION:
     *     设置锁屏/唤醒事件监听，使用Apple官方推荐的API
     *     遵循Apple官方指导原则，不使用私有API
     */
    private func setupScreenLockUnlockNotifications() {
        let notificationCenter = NotificationCenter.default

        // 监听设备解锁事件（仅在设备有密码保护时有效）
        notificationCenter.addObserver(
            self,
            selector: #selector(deviceDidUnlock),
            name: UIApplication.protectedDataDidBecomeAvailableNotification,
            object: nil
        )

        // 监听设备即将锁定事件
        notificationCenter.addObserver(
            self,
            selector: #selector(deviceWillLock),
            name: UIApplication.protectedDataWillBecomeUnavailableNotification,
            object: nil
        )

        // 监听应用回到前台事件（通过health检查VPN extension健康状况）
        notificationCenter.addObserver(
            self,
            selector: #selector(handleApplicationWillEnterForeground),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )

        // 监听应用进入后台事件
        notificationCenter.addObserver(
            self,
            selector: #selector(handleApplicationDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )

        NSLog("🔒 [AppDelegate] Screen lock/unlock notifications setup completed")

        // 统一增强：确保通知监听器不被编译器优化掉
        // 强制保持对通知中心的引用，防止被编译器优化
        objc_setAssociatedObject(self, "notificationCenter", notificationCenter, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)

        // 验证通知监听器是否正确设置
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            let observers = notificationCenter.description
            os_log("🔒 [UNIFIED] Notification observers verified: %{public}@", log: OSLog.default, type: .info, observers)
        }
    }

    /**
     * NAME: deviceWillLock
     *
     * DESCRIPTION:
     *     设备即将锁定时的处理逻辑
     *     记录锁屏时间和VPN连接状态，不主动断开VPN
     *     VPN Extension会通过disconnectOnSleep自动处理断开
     */
    @objc private func deviceWillLock() {
        lastLockTime = Date()
        print("🔍 [SleepMonitor_DEBUG] Device will lock notification received at: \(lastLockTime!)")

        // 统一增强：确保通知方法被正确调用
        os_log("🔒 [UNIFIED] Device will lock notification triggered at: %{public}@", log: OSLog.default, type: .info, "\(lastLockTime!)")

        // 只记录当前VPN连接状态，不主动断开
        // VPN Extension已设置disconnectOnSleep，会自动处理sleep时的断开
        if let handler = platformChannelHandler {
            print("🔍 [SleepMonitor_DEBUG] PlatformChannelHandler available, recording VPN status")
            Task {
                let isConnected = await handler.isVPNConnected()
                let vpnState = await handler.getVPNConnectionState()
                wasVPNConnectedBeforeLock = isConnected

                print("🔍 [SleepMonitor_DEBUG] VPN status before lock - connected: \(isConnected), state: \(vpnState)")
                NSLog("🔒 [AppDelegate] Device will lock - VPN was connected: \(wasVPNConnectedBeforeLock)")

                os_log("🔒 [UNIFIED] VPN state before lock: connected=%{public}@", log: OSLog.default, type: .info, "\(wasVPNConnectedBeforeLock)")

                // 不主动断开VPN - 由VPN Extension的disconnectOnSleep和sleep()方法处理
                print("🔍 [SleepMonitor_DEBUG] VPN Extension will handle disconnect on sleep automatically")
            }
        } else {
            print("🔍 [SleepMonitor_DEBUG] PlatformChannelHandler not available")
        }
    }

    /**
     * NAME: deviceDidUnlock
     *
     * DESCRIPTION:
     *     设备解锁时的处理逻辑
     *     如果之前连接但现在不是connected状态，则重连
     */
    @objc private func deviceDidUnlock() {
        // 防止重复处理解锁事件
        guard !isProcessingUnlock else {
            print("🔍 [SimpleReconnect_DEBUG] Device unlock already being processed, skipping duplicate")
            NSLog("⚠️ [AppDelegate] Device unlock already being processed, skipping duplicate")
            return
        }

        isProcessingUnlock = true
        defer {
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                self.isProcessingUnlock = false
            }
        }

        print("🔍 [SimpleReconnect_DEBUG] Device unlock notification received")
        NSLog("🔓 [AppDelegate] Device unlocked")

        // 简单逻辑：如果之前连接但现在不是connected，就重连
        checkAndReconnectIfNeeded(reason: "device_unlock")
    }





    /**
     * NAME: handleApplicationWillEnterForeground
     *
     * DESCRIPTION:
     *     应用回到前台时的处理逻辑
     *     如果之前连接但现在不是connected状态，则重连
     */
    @objc private func handleApplicationWillEnterForeground() {
        print("🔍 [SimpleReconnect_DEBUG] Application will enter foreground")
        NSLog("📱 [AppDelegate] Application will enter foreground")

        // 延迟检查，确保应用完全激活
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.checkAndReconnectIfNeeded(reason: "app_foreground")
        }
    }

    /**
     * NAME: checkAndReconnectIfNeeded
     *
     * DESCRIPTION:
     *     简单重连逻辑：如果之前连接但现在不是connected状态，则重连
     *     包含防重复和超时保护机制
     *
     * PARAMETERS:
     *     reason - 触发重连检查的原因
     */
    private func checkAndReconnectIfNeeded(reason: String) {
        print("🔍 [SimpleReconnect_DEBUG] === Starting reconnection check ===")
        print("🔍 [SimpleReconnect_DEBUG] Reason: \(reason)")

        // 防止重复重连检查
        guard !isReconnectionCheckInProgress else {
            print("🔍 [SimpleReconnect_DEBUG] ⚠️ Reconnection check already in progress, skipping")
            NSLog("⚠️ [AppDelegate] Reconnection check already in progress, skipping duplicate call")
            return
        }

        guard let handler = platformChannelHandler else {
            print("🔍 [SimpleReconnect_DEBUG] PlatformChannelHandler not available")
            return
        }

        // 如果之前没有连接，不需要重连
        guard wasVPNConnectedBeforeLock else {
            print("🔍 [SimpleReconnect_DEBUG] VPN was not connected before, skipping reconnection")
            return
        }

        // 检查是否在最小重连间隔内
        if let lastReconnect = lastReconnectTime {
            let timeSinceLastReconnect = Date().timeIntervalSince(lastReconnect)
            print("🔍 [SimpleReconnect_DEBUG] Time since last reconnect: \(timeSinceLastReconnect)s")

            if timeSinceLastReconnect < minimumReconnectInterval {
                print("🔍 [SimpleReconnect_DEBUG] Skipping - within minimum interval (\(minimumReconnectInterval)s)")
                NSLog("⏰ [AppDelegate] Skipping reconnection - too soon since last attempt")
                return
            }
        }

        isReconnectionCheckInProgress = true
        defer {
            isReconnectionCheckInProgress = false
            print("🔍 [SimpleReconnect_DEBUG] Reconnection check flag reset")
        }

        Task {
            // 检查当前VPN状态
            let currentState = await handler.getVPNConnectionState()
            print("🔍 [SimpleReconnect_DEBUG] Current VPN state: \(currentState)")

            // 如果不是connected状态，触发重连
            if !currentState.contains("connected") {
                print("🔍 [SimpleReconnect_DEBUG] VPN not connected, triggering reconnection for reason: \(reason)")
                NSLog("📱 [AppDelegate] VPN not connected, triggering reconnection for: \(reason)")

                // 更新重连时间
                lastReconnectTime = Date()
                print("🔍 [SimpleReconnect_DEBUG] Updated last reconnect time")

                let success = await handler.triggerScreenLockRecoveryReconnection(
                    reason: reason,
                    lockDuration: 0
                )

                print("🔍 [SimpleReconnect_DEBUG] Reconnection result: \(success)")

                if success {
                    NSLog("✅ [AppDelegate] VPN reconnection triggered successfully")
                } else {
                    NSLog("❌ [AppDelegate] VPN reconnection failed to trigger")
                }
            } else {
                print("🔍 [SimpleReconnect_DEBUG] VPN already connected, no reconnection needed")
            }
        }
    }

    /**
     * NAME: handleApplicationDidEnterBackground
     *
     * DESCRIPTION:
     *     应用进入后台时的处理逻辑
     *     记录VPN连接状态以备后续恢复使用
     */
    @objc private func handleApplicationDidEnterBackground() {
        print("🔍 [SimpleReconnect_DEBUG] Application did enter background")
        NSLog("📱 [AppDelegate] Application did enter background")

        // 记录当前VPN连接状态
        if let handler = platformChannelHandler {
            Task {
                let isConnected = await handler.isVPNConnected()
                wasVPNConnectedBeforeLock = isConnected

                print("🔍 [SimpleReconnect_DEBUG] VPN status when backgrounded - connected: \(isConnected)")
                NSLog("📱 [AppDelegate] App backgrounded - VPN connected: \(isConnected)")
            }
        }
    }





    // MARK: - Cleanup

    deinit {
        // 清理通知监听
        NotificationCenter.default.removeObserver(self)
    }
}